package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.HashMap;
import java.util.Map;

public enum ESubBudgetPeriod implements PersistentEnum<String> {
    DAILY("DAILY", "Day"),
    MONTHLY("MONTHLY", "Month");

    private static final Map<String, ESubBudgetPeriod> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ESubBudgetPeriod e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    ESubBudgetPeriod(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static ESubBudgetPeriod of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ESubBudgetPeriod, String> {
        public Converter() {
            super(ESubBudgetPeriod.class);
        }
    }

    public static class RequestQueryConverter
            implements org.springframework.core.convert.converter.Converter<String, ESubBudgetPeriod> {
        @Override
        public ESubBudgetPeriod convert(String source) {
            for (ESubBudgetPeriod e : values()) {
                if (e.getValue().equals(source))
                    return e;
            }
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Status type mismatch ", source);
        }
    }
}