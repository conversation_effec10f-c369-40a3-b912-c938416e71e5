package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.HashMap;
import java.util.Map;

public enum ETransactionBatchType implements PersistentEnum<String> {
    EARN("EARN", "Earn"),
    BURN("BURN", "Burn"),
    SALE("SALE", "Sale"),
    ADJUST("ADJUST", "Adjust"),
    REVERT_FULL("REVERT_FULL", "Revert full"),
    REVERT_PARTIAL("REVERT_PARTIAL", "Revert partial"),
    REVERT_POINT("REVERT_POINT", "Revert point"),;

    private static final Map<String, ETransactionBatchType> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ETransactionBatchType e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    private ETransactionBatchType(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static ETransactionBatchType of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ETransactionBatchType, String> {
        public Converter() {
            super(ETransactionBatchType.class);
        }
    }

    public static class RequestQueryConverter implements org.springframework.core.convert.converter.Converter<String, ETransactionBatchType> {
        @Override
        public ETransactionBatchType convert(String source) {
            for (ETransactionBatchType e : values()) {
                if (e.getValue().equals(source))
                    return e;
            }
            throw new BusinessException(ErrorCode.BAD_REQUEST, "ETransactionBatchType mismatch ", source);
        }
    }
}
