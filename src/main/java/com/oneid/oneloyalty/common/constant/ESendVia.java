package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;

import java.util.HashMap;
import java.util.Map;

public enum ESendVia implements PersistentEnum<String> {
    SFTP("SFTP", "SFTP"),
    Manual("M", "Manual");

    private static final Map<String, ESendVia> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ESendVia e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    ESendVia(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static ESendVia of(String value) {
        return mapByValue.get(value);
    }

    public static class Converter extends PersistentEnumConverter<ESendVia, String> {
        public Converter() {
            super(ESendVia.class);
        }
    }
}