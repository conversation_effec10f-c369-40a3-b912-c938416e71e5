package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.HashMap;
import java.util.Map;

public enum ETierAdjustmentProcessStatus implements PersistentEnum<String> {

    PENDING("P", "Pending"),
    SUCCESS("S", "Success"),
    FAILED("F", "Failed");

    private static final Map<String, ETierAdjustmentProcessStatus> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ETierAdjustmentProcessStatus e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    ETierAdjustmentProcessStatus(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;
    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static ETierAdjustmentProcessStatus of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ETierAdjustmentProcessStatus, String> {
        public Converter() {
            super(ETierAdjustmentProcessStatus.class);
        }
    }

    public static class RequestQueryConverter implements org.springframework.core.convert.converter.Converter<String, ETierAdjustmentProcessStatus> {
        @Override
        public ETierAdjustmentProcessStatus convert(String source) {
            for (ETierAdjustmentProcessStatus e : values()) {
                if (e.getValue().equals(source))
                    return e;
            }
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Process Status mismatch ", source);
        }
    }

}
