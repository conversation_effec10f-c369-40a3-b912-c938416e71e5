package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum ETransactionCodeType implements PersistentEnum<String> {

    FIN("FIN", "Financial"),
    NON_FIN("NON_FIN", "Non-Financial");

    private static final Map<String, ETransactionCodeType> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ETransactionCodeType e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    private final String value;
    private final String displayName;

    ETransactionCodeType(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static ETransactionCodeType of(String value) {
        return mapByValue.get(value);
    }

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ETransactionCodeType, String> {
        public Converter() {
            super(ETransactionCodeType.class);
        }
    }
}
