package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;

import java.util.HashMap;
import java.util.Map;

public enum RoundingRule implements PersistentEnum<String> {

    NEAREST("NEAREST", "Nearest"),
    UP("UP", "Round Up"),
    DOWN("DOWN", "Round Down");

    private static final Map<String, RoundingRule> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (RoundingRule e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    RoundingRule(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static RoundingRule of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<RoundingRule, String> {
        public Converter() {
            super(RoundingRule.class);
        }
    }
    }
