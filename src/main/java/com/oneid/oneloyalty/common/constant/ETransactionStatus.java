package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.HashMap;
import java.util.Map;

public enum ETransactionStatus implements PersistentEnum<String> {

    PROCESSING("R","Processing"),
    PENDING("P", "Pending"),
    SUCCESS("S", "Success"),
    HOLDING("H", "Holding"),
    FAIL("F", "Fail"),
    CANCEL("C", "Cancel");

    private static final Map<String, ETransactionStatus> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ETransactionStatus e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    ETransactionStatus(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static ETransactionStatus of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ETransactionStatus, String> {
        public Converter() {
            super(ETransactionStatus.class);
        }
    }

    public static class RequestQueryConverter implements org.springframework.core.convert.converter.Converter<String, ETransactionStatus> {
        @Override
        public ETransactionStatus convert(String source) {
            for (ETransactionStatus e : values()) {
                if (e.getValue().equals(source))
                    return e;
            }
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Transaction Status type mismatch ", source);
        }
    }

}
