package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.HashMap;
import java.util.Map;

public enum EUniqueLevel implements PersistentEnum<String> {
	L0("L0", "Unique Level 0"), 
	L1("L1", "Unique Level 1");

	private static final Map<String, EUniqueLevel> mapByValue;

	static {
		mapByValue = new HashMap<>();
		for (EUniqueLevel e : values()) {
			mapByValue.put(e.getValue(), e);
		}
	}

	private EUniqueLevel(String value, String displayName) {
		this.value = value;
		this.displayName = displayName;
	}

	private final String value;

	private final String displayName;

	@Override
	@JsonValue
	public String getValue() {
		return this.value;
	}

	public static EUniqueLevel of(String value) {
		return mapByValue.get(value);
	}

	@Override
	public String getDisplayName() {
		return this.displayName;
	}

	public static class Converter extends PersistentEnumConverter<EUniqueLevel, String> {
		public Converter() {
			super(EUniqueLevel.class);
		}
	}

	public static class RequestQueryConverter
			implements org.springframework.core.convert.converter.Converter<String, EUniqueLevel> {
		@Override
		public EUniqueLevel convert(String source) {
			for (EUniqueLevel e : values()) {
				if (e.getValue().equals(source))
					return e;
			}
			throw new BusinessException(ErrorCode.BAD_REQUEST, "Status type mismatch ", source);
		}
	}
}
