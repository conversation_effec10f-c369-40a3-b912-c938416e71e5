package com.oneid.oneloyalty.common.constant;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum ESeqName {

    MEMBER_CODE("member_code_seq", 12),
    USER_PROFILE_CODE("user_profile_id_seq", 10),
    MEMBER_PRODUCT_ACCOUNT_CODE("member_account_code_seq", 16),
    TXN_REF("transaction_ref_seq", 15),
    POST_PURCHASE_REF("post_purchase_ref_seq",15);

    private static final Map<String, ESeqName> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ESeqName e : values()) {
            mapByValue.put(e.sqName, e);
        }
    }

    ESeqName(String seqName, int maxLength) {
        this.sqName = seqName;
        this.maxLength = maxLength;
    }

    private final String sqName;
    private final int maxLength;
}