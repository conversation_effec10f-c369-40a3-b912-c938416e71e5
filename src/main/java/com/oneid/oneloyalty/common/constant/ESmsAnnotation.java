package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum ESmsAnnotation implements PersistentEnum<String> {
    CARD_NO("{CardNo}", "Member card number"),
    MOBILE("{Mobile}", "Member phone number"),
    USER_ID("{UserID}", "User Profile Master ID"),
    MEMBER_CODE("{MemberCode}", "Member code (CSN)"),
    AWARD_POINT("{AwardPoint}", "Amount of point awarded to member"),
    REDEEM_POINT("{RedeemPoint}", "Amount of point redeemed from member"),
    TXN_DATE("{TxnDate}", "Transaction time");

    private static final Map<String, ESmsAnnotation> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ESmsAnnotation e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    private ESmsAnnotation(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static ESmsAnnotation of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static List<ESmsAnnotation> getAll() {
        return Arrays.asList(values());
    }

    public static class Converter extends PersistentEnumConverter<ESmsAnnotation, String> {
        public Converter() {
            super(ESmsAnnotation.class);
        }
    }

    public static class RequestQueryConverter implements org.springframework.core.convert.converter.Converter<String, ESmsAnnotation> {
        @Override
        public ESmsAnnotation convert(String source) {
            for (ESmsAnnotation e : values()) {
                if (e.getValue().equals(source))
                    return e;
            }
            throw new BusinessException(ErrorCode.BAD_REQUEST, "ESmsAnnotation mismatch ", source);
        }
    }
}
