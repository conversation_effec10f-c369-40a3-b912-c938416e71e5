package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.HashMap;
import java.util.Map;

public enum ETierBenefitType implements PersistentEnum<String> {
	PRIVILEGE("PRIVILEGE", "Privilege"), 
	AWARD("AWARD", "Award");

	private static final Map<String, ETierBenefitType> mapByValue;

	static {
		mapByValue = new HashMap<>();
		for (ETierBenefitType e : values()) {
			mapByValue.put(e.getValue(), e);
		}
	}

	private ETierBenefitType(String value, String displayName) {
		this.value = value;
		this.displayName = displayName;
	}

	private final String value;

	private final String displayName;

	@Override
	@JsonValue
	public String getValue() {
		return this.value;
	}

	public static ETierBenefitType of(String value) {
		return mapByValue.get(value);
	}

	@Override
	public String getDisplayName() {
		return this.displayName;
	}

	public static class Converter extends PersistentEnumConverter<ETierBenefitType, String> {
		public Converter() {
			super(ETierBenefitType.class);
		}
	}

	public static class RequestQueryConverter
			implements org.springframework.core.convert.converter.Converter<String, ETierBenefitType> {
		@Override
		public ETierBenefitType convert(String source) {
			for (ETierBenefitType e : values()) {
				if (e.getValue().equals(source))
					return e;
			}
			throw new BusinessException(ErrorCode.BAD_REQUEST, "Status type mismatch ", source);
		}
	}
}
