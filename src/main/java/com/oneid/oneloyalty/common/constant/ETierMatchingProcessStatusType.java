package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import com.oneid.oneloyalty.common.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ETierMatchingProcessStatusType implements PersistentEnum<String> {
    CHECK_INFO_REQUESTED("CIR", "CheckInfoRequested"),
    OTP_REQUESTED("OR", "OtpRequested"),
    OTP_VERIFIED("OV", "OtpVerified"),
    CONFIRM_REQUESTED("CR", "ConfirmRequested"),
    SUCCESS("S", "Success");

    @JsonValue
    private final String value;
    
    private final String displayName;
    
    public static ETierMatchingProcessStatusType lookup(String value) {
        if (StringUtils.isEmpty(value))
            return null;
        
        return Stream.of(values())
                .filter(each -> each.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
    
    public static class Converter extends PersistentEnumConverter<ETierMatchingProcessStatusType, String> {
        public Converter() {
            super(ETierMatchingProcessStatusType.class);
        }
    }
    
    public static class RequestQueryConverter implements org.springframework.core.convert.converter.Converter<String, ETierMatchingProcessStatusType> {
        @Override
        public ETierMatchingProcessStatusType convert(String source) {
            for (ETierMatchingProcessStatusType e : values()) {
                if (e.getValue().equals(source))
                    return e;
            }
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Tier matching process status mismatch", source);
        }
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }
}
