package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;

import java.util.HashMap;
import java.util.Map;

public enum ESettlementMode implements PersistentEnum<String> {

    NEAREST("AUTO", "Auto"),

    DOWN("BATCH_RECON", "Batch/recon");

    private static final Map<String, ESettlementMode> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ESettlementMode e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    ESettlementMode(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static ESettlementMode of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ESettlementMode, String> {
        public Converter() {
            super(ESettlementMode.class);
        }
    }
}
