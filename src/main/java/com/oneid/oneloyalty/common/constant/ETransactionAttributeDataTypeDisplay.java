package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.HashMap;
import java.util.Map;

public enum ETransactionAttributeDataTypeDisplay implements PersistentEnum<String> {
    NUMBER("NUMBER", "Number"),
    TEXT("TEXT", "Text"),
    DATE("DATE", "Date"),
    DATE_TIME("DATE_TIME", "Date time"),
    COMBOBOX("COMBOBOX", "Combobox");

    private static final Map<String, ETransactionAttributeDataTypeDisplay> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ETransactionAttributeDataTypeDisplay e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    private ETransactionAttributeDataTypeDisplay(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static ETransactionAttributeDataTypeDisplay of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ETransactionAttributeDataTypeDisplay, String> {
        public Converter() {
            super(ETransactionAttributeDataTypeDisplay.class);
        }
    }

    public static class RequestQueryConverter implements org.springframework.core.convert.converter.Converter<String, ETransactionAttributeDataTypeDisplay> {
        @Override
        public ETransactionAttributeDataTypeDisplay convert(String source) {
            for (ETransactionAttributeDataTypeDisplay e : values()) {
                if (e.getValue().equals(source))
                    return e;
            }
            throw new BusinessException(ErrorCode.BAD_REQUEST, "ETransactionAttributeDataTypeDisplay mismatch ", source);
        }
    }
}
