package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.HashMap;
import java.util.Map;

public enum ETierAdjustmentBatchProcessStatus implements PersistentEnum<String> {

    PENDING("PENDING", "Pending"),
    PROCESSING("PROCESSING", "Processing"),
    COMPLETED("COMPLETED", "Completed");

    private static final Map<String, ETierAdjustmentBatchProcessStatus> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ETierAdjustmentBatchProcessStatus e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    ETierAdjustmentBatchProcessStatus(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;
    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static ETierAdjustmentBatchProcessStatus of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ETierAdjustmentBatchProcessStatus, String> {
        public Converter() {
            super(ETierAdjustmentBatchProcessStatus.class);
        }
    }

    public static class RequestQueryConverter implements org.springframework.core.convert.converter.Converter<String, ETierAdjustmentBatchProcessStatus> {
        @Override
        public ETierAdjustmentBatchProcessStatus convert(String source) {
            for (ETierAdjustmentBatchProcessStatus e : values()) {
                if (e.getValue().equals(source))
                    return e;
            }
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Batch Process Status mismatch ", source);
        }
    }
}
