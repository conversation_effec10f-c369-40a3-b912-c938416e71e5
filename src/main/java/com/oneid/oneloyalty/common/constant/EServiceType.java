package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import com.oneid.oneloyalty.common.exception.BusinessException;

import java.util.HashMap;
import java.util.Map;

public enum EServiceType implements PersistentEnum<String> {
    COUNTER("COUNTER", "COUNTER"),
    CALENDAR_EVENT("CALENDAR_EVENT", "CALE<PERSON>AR_EVENT"),
    LIMITATION("LIM<PERSON>AT<PERSON>", "LIMITATION"),
    PERMISSION("PERMISSION", "PERMISSION"),
    TIER("TIER", "TIER"),
    SCHEME("SCHEME", "SCHEME"),
    BENEFIT("BENEFIT", "BENEFIT"),
    VOUCHER_PROMO("VOUCHER_PROMO", "VOUCHER_PROMO"),
    MEMBER_STATUS("MEMBER_STATUS", "MEMBER_STATUS"),
    ESCROW_POINT("ESCROW_POINT", "ESCROW_POINT"),
    POINT_EXCHANGE("POINT_EXCHANGE", "POINT_EXCHANGE"),
    SCHEME_FORMULA("SCHEME_FORMULA", "SCHEME_FORMULA"),
    VOUCHER_PROMO_NR("VOUCHER_PROMO_NR", "VOUCHER_PROMO_NR");

    private static final Map<String, EServiceType> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (EServiceType e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    private EServiceType(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static EServiceType of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<EServiceType, String> {
        public Converter() {
            super(EServiceType.class);
        }
    }

    public static class RequestQueryConverter
            implements org.springframework.core.convert.converter.Converter<String, EServiceType> {
        @Override
        public EServiceType convert(String source) {
            for (EServiceType e : values()) {
                if (e.getValue().equals(source))
                    return e;
            }
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Status type mismatch ", source);
        }
    }
}
