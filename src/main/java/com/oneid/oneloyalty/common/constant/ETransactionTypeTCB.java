package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;

import java.util.HashMap;
import java.util.Map;

public enum ETransactionTypeTCB implements PersistentEnum<String> {

    RP("RP", "RP"),
    REV("REV", "REV"),
    CB("CB", "CB"),
    RP_REV("RP_REV", "RP_REV");

    private static final Map<String, ETransactionTypeTCB> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ETransactionTypeTCB e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    ETransactionTypeTCB(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static ETransactionTypeTCB of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ETransactionTypeTCB, String> {
        public Converter() {
            super(ETransactionTypeTCB.class);
        }
    }
}
