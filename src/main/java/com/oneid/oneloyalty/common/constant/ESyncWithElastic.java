package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;

import java.util.HashMap;
import java.util.Map;

public enum ESyncWithElastic implements PersistentEnum<String> {

    NO("N", "No"),
    YES("Y", "Yes"),
    FAIL("F", "Fail");

    private static final Map<String, ESyncWithElastic> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ESyncWithElastic e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    ESyncWithElastic(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static ESyncWithElastic of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ESyncWithElastic, String> {
        public Converter() {
            super(ESyncWithElastic.class);
        }
    }
}
