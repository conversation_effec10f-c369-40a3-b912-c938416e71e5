package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

public enum ETransactionValue implements PersistentEnum<String> {

    GMV("GMV", "gmv", "gmv"),
    NETT_AMOUNT("NETT_AMOUNT", "nettAmount", "nett_amount"),
    GROSS_AMOUNT("GROSS_AMOUNT", "grossAmount", "gross_amount");

    private static final Map<String, ETransactionValue> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ETransactionValue e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    private final String value;
    private final String displayName;
    @Getter
    private final String queryName;

    ETransactionValue(String value, String displayName, String queryName) {
        this.value = value;
        this.displayName = displayName;
        this.queryName = queryName;
    }

    public static ETransactionValue of(String value) {
        return mapByValue.get(value);
    }

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ETransactionValue, String> {
        public Converter() {
            super(ETransactionValue.class);
        }
    }
}
