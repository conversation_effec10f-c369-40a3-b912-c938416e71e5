package com.oneid.oneloyalty.common.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum ETransactionType implements PersistentEnum<String> {

    AWARD("AWD", "Award"),
    REDEEM("RED", "Redemption"),
    SALE("SALE", "Sale"),
    ADJUSTMENT("ADJ", "Adjustment"),
    REFUND("REF", "Refund"),
    TRANSFER("PTT", "Transfer"),
    EXPIRE("EXP", "Expire"),
    REVERT_EXPIRE("REVERT_EXP", "Revert Expire"),
    REVOKE_HOLDING_POINTS("REVOKE_HOLDING_POINTS", "Revoke Holding Points");

    private static final Map<String, ETransactionType> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (ETransactionType e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    ETransactionType(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static ETransactionType of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<ETransactionType, String> {
        public Converter() {
            super(ETransactionType.class);
        }
    }
}
