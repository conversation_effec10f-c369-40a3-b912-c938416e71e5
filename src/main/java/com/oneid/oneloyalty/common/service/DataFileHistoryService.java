package com.oneid.oneloyalty.common.service;

import com.oneid.oneloyalty.common.entity.DataFileHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface DataFileHistoryService {

    DataFileHistory find(String fileName, String controlFileName);

    List<DataFileHistory> findByControlFileId(Long controlFileId);

    Page<DataFileHistory> findAllByControlFileId(Long controlFileId, Pageable pageable);

    Long sumTotalTransactionByControlFileId(Long controlFileId);

    Long sumTotalFailedTransactionByControlFileId(Long controlFileId);
}
