package com.oneid.oneloyalty.common.service;

import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface ControlFileHistoryService {
    ControlFileHistory findById(Long id);

    ControlFileHistory save(ControlFileHistory controlFileHistory);

    ControlFileHistory findByFileName(String fileName, EFileType fileType);

    Long countPendingFiles(String fileName, EFileType fileType);

    Page<ControlFileHistory> filter(String controlFileName,
                                    EProcessingStatus processingStatus,
                                    List<EFileType> fileTypes,
                                    Date createdAtFrom,
                                    Date createdAtTo,
                                    Date updatedAtFrom,
                                    Date updatedAtTo, Pageable pageable);

    Page<ControlFileHistory> getListAvailable(String name,
                                              EProcessingStatus status,
                                              EFileType fileType,
                                              Date createStartDate,
                                              Date createEndDate,
                                              Date updateStartDate,
                                              Date updateEndDate,
                                              Integer offset,
                                              Integer limit);
}
