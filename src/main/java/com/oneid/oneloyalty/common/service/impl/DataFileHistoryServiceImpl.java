package com.oneid.oneloyalty.common.service.impl;

import com.oneid.oneloyalty.common.entity.DataFileHistory;
import com.oneid.oneloyalty.common.repository.DataFileHistoryRepository;
import com.oneid.oneloyalty.common.service.DataFileHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DataFileHistoryServiceImpl implements DataFileHistoryService {
    @Autowired
    DataFileHistoryRepository dataFileHistoryRepository;

    @Override
    public DataFileHistory find(String fileName, String controlFileName) {
        return dataFileHistoryRepository.find(fileName, controlFileName);
    }

    @Override
    public List<DataFileHistory> findByControlFileId(Long controlFileId) {
        return dataFileHistoryRepository.findByControlFileId(controlFileId);
    }

    @Override
    public Page<DataFileHistory> findAllByControlFileId(Long controlFileId, Pageable pageable) {
        return dataFileHistoryRepository.findAllByControlFileId(controlFileId, pageable);
    }

    @Override
    public Long sumTotalTransactionByControlFileId(Long controlFileId) {
        return dataFileHistoryRepository.sumTotalTransactionByControlFileId(controlFileId);
    }

    @Override
    public Long sumTotalFailedTransactionByControlFileId(Long controlFileId) {
        return dataFileHistoryRepository.sumTotalFailedTransactionByControlFileId(controlFileId);
    }
}
