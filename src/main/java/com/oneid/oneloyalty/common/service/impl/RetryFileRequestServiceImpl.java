package com.oneid.oneloyalty.common.service.impl;

import com.oneid.oneloyalty.common.constant.ErrorCode;
import com.oneid.oneloyalty.common.entity.RetryFileRequest;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.RetryFileRequestHistoryDTO;
import com.oneid.oneloyalty.common.repository.RetryFileRequestRepository;
import com.oneid.oneloyalty.common.service.RetryFileRequestService;
import com.oneid.oneloyalty.common.util.LogData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RetryFileRequestServiceImpl implements RetryFileRequestService {
    @Autowired
    private RetryFileRequestRepository retryFileRequestRepository;

    @Override
    public RetryFileRequest save(RetryFileRequest entity) {
        return null;
    }

    @Override
    public RetryFileRequest findById(String fileName) {
        return retryFileRequestRepository.findByFileName(fileName).orElseThrow(
                () -> new BusinessException(ErrorCode.RETRY_FILE_REQUEST_NOT_FOUND,
                        "Retry file request not found",
                        LogData.createLogData().append("file_name", fileName))
        );
    }

    @Override
    public RetryFileRequest findByOriginalControlFile(Integer originalControlFileId) {
        return retryFileRequestRepository.findByOriginalControlFileId(originalControlFileId).orElseThrow(
                () -> new BusinessException(ErrorCode.ORIGINAL_FILE_NOT_FOUND,
                        "Original file not found",
                        LogData.createLogData().append("original_control_file_id", originalControlFileId))
        );
    }

    @Override
    public List<RetryFileRequestHistoryDTO> getRetryHistoryList(Integer controlFileId) {
        return retryFileRequestRepository.getRetryHistoryList(controlFileId);
    }
}
