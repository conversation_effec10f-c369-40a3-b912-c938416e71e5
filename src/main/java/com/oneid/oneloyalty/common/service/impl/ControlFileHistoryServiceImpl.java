package com.oneid.oneloyalty.common.service.impl;

import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import com.oneid.oneloyalty.common.repository.ControlFileHistoryRepository;
import com.oneid.oneloyalty.common.search.SearchCriteria;
import com.oneid.oneloyalty.common.search.SearchOperation;
import com.oneid.oneloyalty.common.search.SpecificationBuilder;
import com.oneid.oneloyalty.common.service.ControlFileHistoryService;
import com.oneid.oneloyalty.common.util.OffsetBasedPageRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ControlFileHistoryServiceImpl implements ControlFileHistoryService {
    @Autowired
    ControlFileHistoryRepository controlFileHistoryRepository;

    @Override
    public ControlFileHistory findById(Long id) {
        return controlFileHistoryRepository.findById(id).orElse(null);
    }

    @Override
    public ControlFileHistory save(ControlFileHistory controlFileHistory) {
        return controlFileHistoryRepository.save(controlFileHistory);
    }

    @Override
    public ControlFileHistory findByFileName(String fileName, EFileType fileType) {
        return controlFileHistoryRepository.findByFileName(fileName, fileType);
    }

    @Override
    public Long countPendingFiles(String fileName, EFileType fileType) {
        return controlFileHistoryRepository.countPendingFiles(fileName, fileType);
    }

    @Override
    public Page<ControlFileHistory> filter(String controlFileName,
                                           EProcessingStatus processingStatus,
                                           List<EFileType> fileTypes,
                                           Date createdAtFrom,
                                           Date createdAtTo,
                                           Date updatedAtFrom,
                                           Date updatedAtTo,
                                           Pageable pageable
    ) {
        return controlFileHistoryRepository.filter(controlFileName, processingStatus, fileTypes, createdAtFrom, createdAtTo, updatedAtFrom, updatedAtTo, pageable);
    }

    @Override
    public Page<ControlFileHistory> getListAvailable(String name,
                                                     EProcessingStatus status,
                                                     EFileType fileType,
                                                     Date createStartDate,
                                                     Date createEndDate,
                                                     Date updateStartDate,
                                                     Date updateEndDate,
                                                     Integer offset,
                                                     Integer limit) {

        SpecificationBuilder<ControlFileHistory> specification = new SpecificationBuilder<>();

        specification.add(new SearchCriteria("createdAt", createStartDate, SearchOperation.GREATER_THAN_EQUAL_DATE));

        specification.add(new SearchCriteria("createdAt", createEndDate, SearchOperation.LESS_THAN_EQUAL_DATE));

        if (fileType != null) {
            specification.add(new SearchCriteria("fileType", fileType, SearchOperation.EQUAL));
        }

        if (name != null) {
            specification.add(new SearchCriteria("fileName", name, SearchOperation.MATCH));
        }

        if (status != null) {
            specification.add(new SearchCriteria("status", status, SearchOperation.EQUAL));
        }

        if (updateStartDate != null) {
            specification.add(new SearchCriteria("updatedAt", updateStartDate, SearchOperation.GREATER_THAN_EQUAL_DATE));
        }

        if (updateEndDate != null) {
            specification.add(new SearchCriteria("updatedAt", updateEndDate, SearchOperation.LESS_THAN_EQUAL_DATE));
        }

        OffsetBasedPageRequest pageRequest = new OffsetBasedPageRequest(offset, limit, Sort.by(Sort.Direction.DESC, "updatedAt"));

        Page<ControlFileHistory> controlFileHistoryResPage = controlFileHistoryRepository.findAll(specification, pageRequest);

        return new PageImpl<>(controlFileHistoryResPage.getContent(), pageRequest, (int) controlFileHistoryResPage.getTotalElements());
    }
}
