package com.oneid.oneloyalty.common.service;

import com.oneid.oneloyalty.common.entity.RetryFileRequest;
import com.oneid.oneloyalty.common.model.RetryFileRequestHistoryDTO;

import java.util.List;

public interface RetryFileRequestService extends BaseService<RetryFileRequest> {
    RetryFileRequest findById(String fileName);

    RetryFileRequest findByOriginalControlFile(Integer originalControlFileId);

    List<RetryFileRequestHistoryDTO> getRetryHistoryList(Integer controlFileId);

}
