package com.oneid.oneloyalty.common.config;

import com.oneid.oneloyalty.common.util.JsonUtil;
import com.oneid.oneloyalty.common.util.Log;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
public class ConfigParam implements InitializingBean {

    @Value("${app.environment:prod}")
    public String appMode;

    @Override
    public void afterPropertiesSet() throws Exception {
        Log.info("COMMON_CONFIG_PARAM: " + JsonUtil.writeValueAsString(this));
    }

}
