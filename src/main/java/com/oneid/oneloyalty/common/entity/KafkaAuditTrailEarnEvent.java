package com.oneid.oneloyalty.common.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "kafka_audit_trail_earn_event")
@NoArgsConstructor
public class KafkaAuditTrailEarnEvent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "kafka_type")
    private String kafkaType;

    @Column(name = "business_code")
    private String businessCode;

    @Column(name = "program_code")
    private String programCode;

    @Column(name = "currency_code")
    private String currencyCode;

    @Column(name = "corporation_code")
    private String corporationCode;

    @Column(name = "store_code")
    private String storeCode;

    @Column(name = "pos_code")
    private String posCode;

    @Column(name = "txn_ref_no")
    private String txnRefNo;

    @Column(name = "invoice_no")
    private String invoiceNo;

    @Column(name = "transaction_time")
    private String transactionTime;

    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "service_error_code")
    private String serviceErrorCode;

    @Column(name = "service_error_message")
    private String serviceErrorMessage;

    @Column(name = "tcb_error_code")
    private String tcbErrorCode;

    @Column(name = "tcb_error_message")
    private String tcbErrorMessage;

    @Column(name = "transaction_status")
    private String transactionStatus;

    @Column(name = "award_point")
    private BigDecimal awardPoint;

    @Column(name = "message_id")
    private String messageId;

    @Column(name = "timestamp")
    private Long timestamp;

    @Column(name = "message_type")
    private String messageType;

    @Column(name = "loyalty_cus_id")
    private String loyaltyCusId;

    @Column(name = "event_id")
    private String eventId;

    @Column(name = "event_code")
    private String eventCode;

    @Column(name = "event_name")
    private String eventName;

    @Column(name = "event_group")
    private String eventGroup;

    @Column(name = "event_product")
    private String eventProduct;

    @Column(name = "event_amount")
    private String eventAmount;

    @Column(name = "event_date")
    private String eventDate;

    @Column(name = "service_code")
    private String serviceCode;

    @Column(name = "event_type")
    private String eventType;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_at")
    private Date createdAt;

    @Column(name = "ds_partition_date")
    private Date dsPartitionDate;

    @Column(name = "group_id")
    private String groupId;

    @Column(name = "file_name")
    private String fileName;
}