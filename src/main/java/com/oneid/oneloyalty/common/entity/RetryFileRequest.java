package com.oneid.oneloyalty.common.entity;

import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.constant.EProgressStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "retry_file_request")
@Getter
@Setter
@NoArgsConstructor
public class RetryFileRequest extends Base {

	private static final long serialVersionUID = 5549314094862443771L;

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "file_name")
	private String fileName;

	@Column(name = "status")
	@Convert(converter = EProcessingStatus.Converter.class)
	private EProcessingStatus status;

	@Column(name = "error_code")
	private String errorCode;

	@Column(name = "error_message")
	private String errorMessage;

	@Column(name ="original_control_file_id")
	private Integer originalControlFileId;
}