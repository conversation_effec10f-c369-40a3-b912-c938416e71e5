package com.oneid.oneloyalty.common.repository.custom;

import com.oneid.oneloyalty.common.entity.RetryFileRequest;
import com.oneid.oneloyalty.common.model.RetryFileRequestHistoryDTO;

import java.util.List;

public interface CustomRetryFileRequestRepository {
    List<RetryFileRequestHistoryDTO> getRetryHistoryList(Integer controlFileId);

    List<RetryFileRequest> getRetryFileRequestList(Integer controlFileId);
}
