package com.oneid.oneloyalty.common.repository;

import com.oneid.oneloyalty.common.entity.KafkaAuditTrailEarnEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface KafkaAuditTrailEarnEventRepository extends
        JpaRepository<KafkaAuditTrailEarnEvent, Long>, JpaSpecificationExecutor<KafkaAuditTrailEarnEvent> {
}