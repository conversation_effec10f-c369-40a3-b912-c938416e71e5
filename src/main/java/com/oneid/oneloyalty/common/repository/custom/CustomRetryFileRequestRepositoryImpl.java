package com.oneid.oneloyalty.common.repository.custom;

import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.entity.RetryFileRequest;
import com.oneid.oneloyalty.common.model.RetryFileRequestHistoryDTO;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class CustomRetryFileRequestRepositoryImpl implements CustomRetryFileRequestRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<RetryFileRequestHistoryDTO> getRetryHistoryList(Integer controlFileId) {
        String sql = "SELECT " +
                "cfh3.ID as id, " +
                "rfr.FILE_NAME as fileName, " +
                "cfh3.STATUS as status, " +
                "cfh3.CREATED_AT as createdAt, " +
                "rfr.CREATED_BY as createdBy " +
                "FROM RETRY_FILE_REQUEST rfr " +
                "INNER JOIN CONTROL_FILE_HISTORY cfh ON rfr.ORIGINAL_CONTROL_FILE_ID = cfh.ID " +
                "INNER JOIN CONTROL_FILE_HISTORY cfh3 ON cfh3.FILE_NAME = rfr.FILE_NAME " +
                "START WITH rfr.ORIGINAL_CONTROL_FILE_ID = :controlFileId " +
                "CONNECT BY PRIOR (" +
                "SELECT cfh2.ID " +
                "FROM CONTROL_FILE_HISTORY cfh2 " +
                "WHERE cfh2.FILE_NAME = rfr.FILE_NAME " +
                ") = rfr.ORIGINAL_CONTROL_FILE_ID " +
                "ORDER BY cfh.CREATED_AT DESC";

        Query query = entityManager.createNativeQuery(sql);
        query.setParameter("controlFileId", controlFileId);

        List<Object[]> results = query.getResultList();
        List<RetryFileRequestHistoryDTO> historyList = new ArrayList<>();

        for (Object[] row : results) {
            Integer id = row[0] != null ? ((BigDecimal) row[0]).intValue() : null;
            String fileName = (String) row[1];
            String statusStr = (String) row[2];
            EProcessingStatus status = statusStr != null ? EProcessingStatus.valueOf(statusStr) : null;
            Date createdAt = row[3] != null ? new Date(((Timestamp) row[3]).getTime()) : null;
            String createdBy = (String) row[4];

            RetryFileRequestHistoryDTO historyRes = RetryFileRequestHistoryDTO.builder()
                    .id(id)
                    .fileName(fileName)
                    .status(status)
                    .createdBy(createdBy)
                    .createdAt(createdAt)
                    .build();

            historyList.add(historyRes);
        }

        return historyList;
    }

    @Override
    public List<RetryFileRequest> getRetryFileRequestList(Integer controlFileId) {
        String sql = "SELECT rfr.* " +
                "FROM RETRY_FILE_REQUEST rfr " +
                "INNER JOIN CONTROL_FILE_HISTORY cfh ON rfr.ORIGINAL_CONTROL_FILE_ID = cfh.ID " +
                "START WITH rfr.ORIGINAL_CONTROL_FILE_ID = :controlFileId " +
                "CONNECT BY PRIOR (" +
                "SELECT cfh2.ID " +
                "FROM CONTROL_FILE_HISTORY cfh2 " +
                "WHERE cfh2.FILE_NAME = rfr.FILE_NAME " +
                ") = rfr.ORIGINAL_CONTROL_FILE_ID ";

        Query query = entityManager.createNativeQuery(sql);
        query.setParameter("controlFileId", controlFileId);

        List<Object[]> results = query.getResultList();
        List<RetryFileRequest> historyList = new ArrayList<>();


        return historyList;
    }
}
