package com.oneid.oneloyalty.common.repository;

import com.oneid.oneloyalty.common.entity.RetryFileRequest;
import com.oneid.oneloyalty.common.repository.custom.CustomRetryFileRequestRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface RetryFileRequestRepository extends JpaRepository<RetryFileRequest, Integer>,
        JpaSpecificationExecutor<RetryFileRequest>, CustomRetryFileRequestRepository {

    Optional<RetryFileRequest> findByFileName(String fileName);

    Optional<RetryFileRequest> findByOriginalControlFileId(Integer originalControlFileId);
}
