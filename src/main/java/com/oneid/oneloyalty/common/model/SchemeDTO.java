package com.oneid.oneloyalty.common.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.ESchemeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SchemeDTO {
    private Integer schemeId;

    private String schemeCode;

    private String schemeName;

    private ESchemeType schemeType;

    private Integer poolId;

    private String poolName;

    private Date startDate;

    private Date endDate;

    private ECommonStatus status;

    private Integer businessId;

    private String businessName;

    private Integer programId;

    private String programName;
}
