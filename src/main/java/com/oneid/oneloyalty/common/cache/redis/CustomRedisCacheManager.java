package com.oneid.oneloyalty.common.cache.redis;

import com.oneid.oneloyalty.common.cache.config.CacheEntity;
import lombok.Getter;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.util.Assert;

import java.time.Duration;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public class CustomRedisCacheManager extends RedisCacheManager {
    private final int defaultTtl;
    private final List<CacheEntity> cacheEntities;
    private final String keyPrefix;
    private final RedisCacheConfiguration defaultCacheConfiguration;
    private final RedisCacheWriter redisCacheWriter;

    public CustomRedisCacheManager(RedisCacheWriter redisCacheWriter,
                                   RedisCacheConfiguration defaultCacheConfiguration,
                                   List<CacheEntity> cacheEntities,
                                   int defaultTtl,
                                   String keyPrefix
    ) {
        super(redisCacheWriter, defaultCacheConfiguration);

        Assert.notNull(cacheEntities, "CacheEntities must not be null!");

        this.defaultCacheConfiguration = defaultCacheConfiguration;
        this.redisCacheWriter = redisCacheWriter;
        this.defaultTtl = defaultTtl;
        this.cacheEntities = cacheEntities;
        this.keyPrefix = keyPrefix;
    }


    @Override
    protected Collection<RedisCache> loadCaches() {
        List<RedisCache> caches = new LinkedList<>();
        for (CacheEntity entity : this.cacheEntities) {
            if (entity.getEnable() == null || entity.getEnable()) {
                int ttl = entity.getExpire() != null ? entity.getExpire().inSeconds() : defaultTtl;
                caches.add(createRedisCache(entity.getName(), getConfiguration(ttl)));
            } else {
                caches.add(createNoOpCache(entity.getName()));
            }
        }

        return caches;
    }

    @Override
    protected RedisCache getMissingCache(String name) {
        return createNoOpCache(name);
    }

    private RedisCache createNoOpCache(String name) {
        return new NoOpRedisCache(name, this.redisCacheWriter, defaultCacheConfiguration);
    }

    private RedisCacheConfiguration getConfiguration(int ttl) {
        return RedisCacheConfiguration.defaultCacheConfig()
                .prefixCacheNameWith(keyPrefix)
                .disableCachingNullValues()
                .entryTtl(Duration.ofSeconds(ttl))
                .serializeValuesWith(RedisSerializationContext
                        .SerializationPair
                        .fromSerializer(RedisSerializer.json())
                );
    }

    public static RedisCacheConfiguration defaultConfiguration(String keyPrefix, int ttl) {
        return RedisCacheConfiguration.defaultCacheConfig()
                .prefixCacheNameWith(keyPrefix)
                .disableCachingNullValues()
                .entryTtl(Duration.ofSeconds(ttl))
                .serializeValuesWith(RedisSerializationContext
                        .SerializationPair
                        .fromSerializer(RedisSerializer.json())
                );
    }
}
