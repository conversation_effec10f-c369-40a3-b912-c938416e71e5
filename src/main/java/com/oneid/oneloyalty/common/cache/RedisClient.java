package com.oneid.oneloyalty.common.cache;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class RedisClient implements CacheClient {

    private static final String KEY_ARGS_PART = "::";

    private final RedisTemplate<String, Object> redisTemplate;

    ValueOperations<String, Object> valueOperations;

    private final ObjectMapper om;

    private String keyPrefix;

    public RedisClient(RedisConnectionFactory connectionFactory) {
        om = new ObjectMapper();
        this.redisTemplate = initTemplate(connectionFactory);
        valueOperations = redisTemplate.opsForValue();
    }

    @Override
    public boolean isExist(String cacheKey, Object... keyArgs) {
        Boolean exist = this.redisTemplate.hasKey(buildKey(cacheKey, keyArgs));

        return exist != null && exist;
    }

    @Override
    public <T> T get(Class<T> toValueType, String key, Object... keyArgs) {
        Object o = valueOperations.get(buildKey(key, keyArgs));
        if (o != null) {
            return om.convertValue(o, toValueType);
        }

        return null;
    }

    @Override
    public void set(Object value, String key, Object... keyArgs) {
        valueOperations.set(buildKey(key, keyArgs), value);
    }

    @Override
    public void set(Object value, long timeout, TimeUnit unit, String key, Object... keyArgs) {
        valueOperations.set(buildKey(key, keyArgs), value, timeout, unit);
    }

    @Override
    public Long getExpire(String cacheKey, Object... keyArgs) {
        return redisTemplate.getExpire(buildKey(cacheKey, keyArgs));
    }

    @Override
    public Long increment(String cacheKey, Object... keyArgs) {
        return valueOperations.increment(buildKey(cacheKey, keyArgs));
    }

    @Override
    public void setExpire(long timeout, TimeUnit unit, String cacheKey, Object... keyArgs) {
        redisTemplate.expire(buildKey(cacheKey, keyArgs), timeout, unit);
    }

    public void setPrefixKey(String prefixKey) {
        this.keyPrefix = prefixKey;
    }

    private String buildKey(String cacheKey, Object... keyArgs) {
        StringBuilder builder = new StringBuilder(cacheKey);
        if (keyPrefix != null) {
            builder.insert(0, keyPrefix + KEY_ARGS_PART);
        }
        if (keyArgs != null && keyArgs.length > 0) {
            for (Object arg : keyArgs) {
                builder.append(KEY_ARGS_PART).append(arg);
            }
        }

        return builder.toString();
    }

    public RedisTemplate<String, Object> initTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new JsonRedisSerializer());
        template.afterPropertiesSet();

        return template;
    }

    class JsonRedisSerializer implements RedisSerializer<Object> {

        public JsonRedisSerializer() {
        }

        @Override
        public byte[] serialize(Object t) throws SerializationException {
            try {
                return om.writeValueAsBytes(t);
            } catch (JsonProcessingException e) {
                throw new SerializationException(e.getMessage(), e);
            }
        }

        @Override
        public Object deserialize(byte[] bytes) throws SerializationException {
            if (bytes == null) {
                return null;
            }
            try {
                return om.readValue(bytes, Object.class);
            } catch (Exception e) {
                throw new SerializationException(e.getMessage(), e);
            }
        }
    }
}
