package com.oneid.oneloyalty.common.cache;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public interface CacheClient {

    boolean isExist(String cacheKey, Object... keyArgs);

    <T> T get(Class<T> valueType, String cacheKey, Object... keyArgs);

    void set(Object value, String cacheKey, Object... keyArgs);

    void set(Object value, long timeout, TimeUnit unit, String cacheKey, Object... keyArgs);

    Long getExpire(String cacheKey, Object... keyArgs);

    Long increment(String cacheKey, Object... keyArgs);

    void setExpire(long timeout, TimeUnit unit, String cacheKey, Object... keyArgs);
}
