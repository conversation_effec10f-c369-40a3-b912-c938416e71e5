package com.oneid.oneloyalty.common.cache.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@Setter
@Getter
public class CacheEntity implements Serializable {

    private static final long serialVersionUID = -454081736192525093L;

    @JsonProperty("name")
    private String name;

    @JsonProperty("enable")
    private Boolean enable;

    @JsonProperty("expire")
    private Expire expire;

    @Setter
    @Getter
    public static class Expire {

        @JsonProperty("unit")
        private ExpireUnit unit;

        @JsonProperty("ttl")
        private Integer ttl;

        public int inSeconds() {
            if (ttl != null) {
                switch (unit) {
                    case SECOND:
                        return ttl;
                    case MINUTE:
                        return ttl * 60;
                    case HOUR:
                        return ttl * 60 * 60;
                }
            }

            return 0;
        }

        public int inMinutes() {
            return inSeconds() / 60;
        }

        public int inHours() {
            return inSeconds() / (60 * 60);
        }

    }

    public enum ExpireUnit {
        SECOND("seconds"),
        MINUTE("minutes"),
        HOUR("hours");

        private final String unit;

        ExpireUnit(String unit) {
            this.unit = unit;
        }

        @JsonValue
        public String getUnit() {
            return unit;
        }
    }

}
