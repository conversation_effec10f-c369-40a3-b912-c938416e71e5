package com.oneid.oneloyalty.common.cache.redis;

import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.lang.Nullable;

import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 */
public class NoOpRedisCache extends RedisCache {

    protected NoOpRedisCache(String name,
                             RedisCacheWriter cacheWriter,
                             RedisCacheConfiguration cacheConfig
    ) {
        super(name, cacheWriter, cacheConfig);
    }

    @Override
    public String getName() {
        return super.getName();
    }

    @Override
    @Nullable
    public ValueWrapper get(Object key) {
        return null;
    }

    @Override
    @Nullable
    public <T> T get(Object key, @Nullable Class<T> type) {
        return null;
    }

    @Override
    @Nullable
    public <T> T get(Object key, Callable<T> valueLoader) {
        try {
            return valueLoader.call();
        } catch (Exception ex) {
            throw new ValueRetrievalException(key, valueLoader, ex);
        }
    }

    @Override
    public void put(Object key, @Nullable Object value) {
    }

    @Override
    @Nullable
    public ValueWrapper putIfAbsent(Object key, @Nullable Object value) {
        return null;
    }

    @Override
    public void evict(Object key) {
    }

    @Override
    public boolean evictIfPresent(Object key) {
        return false;
    }

    @Override
    public void clear() {
    }

    @Override
    public boolean invalidate() {
        return false;
    }

}
