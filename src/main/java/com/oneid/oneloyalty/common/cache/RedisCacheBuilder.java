package com.oneid.oneloyalty.common.cache;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.oneid.oneloyalty.common.cache.config.CacheEntity;
import com.oneid.oneloyalty.common.cache.redis.CustomRedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RedisCacheBuilder {
    private String fileConfig;
    private String keyPrefix;
    private int defaultTTL = 60;
    private final RedisConnectionFactory connectionFactory;

    public RedisCacheBuilder(RedisConnectionFactory connectionFactory) {
        this.connectionFactory = connectionFactory;
    }

    public static RedisCacheBuilder withConnection(RedisConnectionFactory connectionFactory) {
        return new RedisCacheBuilder(connectionFactory);
    }

    public RedisCacheBuilder fileConfigLocation(String fileConfig) {
        this.fileConfig = fileConfig;
        return this;
    }

    public RedisCacheBuilder keyPrefix(String keyPrefix) {
        this.keyPrefix = keyPrefix;
        return this;
    }

    public RedisCacheBuilder defaultTTL(int ttl) {
        this.defaultTTL = ttl;
        return this;
    }

    public RedisCacheManager buildManager() throws Exception {
        List<CacheEntity> cacheEntities = getCacheEntities(this.fileConfig);
        return new CustomRedisCacheManager(
                RedisCacheWriter.lockingRedisCacheWriter(this.connectionFactory),
                CustomRedisCacheManager.defaultConfiguration(this.keyPrefix, this.defaultTTL),
                cacheEntities,
                this.defaultTTL,
                this.keyPrefix
        );
    }

    private List<CacheEntity> getCacheEntities(String fileConfigPath) throws IOException {
        ObjectMapper mapper = new ObjectMapper().registerModule(new SimpleModule());
        TypeReference<List<CacheEntity>> typeReference = new TypeReference<List<CacheEntity>>() {
        };
        InputStream inputStream = new FileInputStream(fileConfigPath);
        return mapper.readValue(inputStream, typeReference);
    }

}